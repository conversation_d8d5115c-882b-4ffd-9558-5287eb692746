const { QuickDB } = require("quick.db");
const db = new QuickDB();
const { setSessionTimeout } = require("../../utils/sessionTimeouts");
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const { generateApiUrl } = require("../../utils/urlSigner");

module.exports = {
  command: "imagevariation",
  aliases: ["imgvar", "variation"],
  category: "utility",
  description: "Create variations of an uploaded image using AI.",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    let userId = event.source.userId;
    if (event.source.type === "group") {
      userId = `${event.source.groupId}:${event.source.userId}`;
    }

    if (event.message.quotedMessageId) {
      const quotedMessageId = event.message.quotedMessageId;
      let stream = await blobClient.getMessageContent(quotedMessageId);
      let chunks = [];

      stream.on("data", (chunk) => {
        chunks.push(chunk);
      });

      stream.on("end", async () => {
        const buffer = Buffer.concat(chunks);
        const timestamp = Date.now();
        const dirPath = path.join(__dirname, "../../static/downloads");
        const filePath = path.join(dirPath, `${timestamp}.jpg`);

        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }

        fs.writeFileSync(filePath, buffer);
        console.log(`Image saved at ${filePath}`);

        const imageUrl = generateApiUrl(`/downloads/${timestamp}.jpg`);

        try {
          const response = await axios.post(
            "http://127.0.0.1:5000/image_variation",
            {
              image_url: imageUrl,
            }
          );

          const variationUrl = response.data.url;

          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "image",
                originalContentUrl: variationUrl,
                previewImageUrl: variationUrl,
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        } catch (error) {
          console.error("Error creating image variation:", error);
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "An error occurred while creating image variation.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        } finally {
          fs.unlink(filePath, (err) => {
            if (err)
              console.error(`Failed to delete image file: ${filePath}`, err);
            else console.log(`Successfully deleted image file: ${filePath}`);
          });
        }
      });

      stream.on("error", async (err) => {
        console.error("Error downloading content:", err);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "An error occurred while downloading the image.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      });
    } else {
      await db.set(`imagevariation_${userId}_active`, true);

      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Please upload an image to create variations.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });

      setSessionTimeout(userId, "imagevariation", async () => {
        console.log(`Image variation session for user ${userId} timed out.`);
        await db.delete(`imagevariation_${userId}_active`);
      });
    }
  },
};
